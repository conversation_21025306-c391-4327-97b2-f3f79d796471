// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		58209F072E4B1CBF0012C395 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 58209EED2E4B1CBD0012C395 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 58209EF42E4B1CBD0012C395;
			remoteInfo = aDictionary;
		};
		58209F112E4B1CBF0012C395 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 58209EED2E4B1CBD0012C395 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 58209EF42E4B1CBD0012C395;
			remoteInfo = aDictionary;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		58209EF52E4B1CBD0012C395 /* aDictionary.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = aDictionary.app; sourceTree = BUILT_PRODUCTS_DIR; };
		58209F062E4B1CBF0012C395 /* aDictionaryTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = aDictionaryTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		58209F102E4B1CBF0012C395 /* aDictionaryUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = aDictionaryUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		58209F182E4B1CBF0012C395 /* Exceptions for "aDictionary" folder in "aDictionary" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 58209EF42E4B1CBD0012C395 /* aDictionary */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		58209EF72E4B1CBD0012C395 /* aDictionary */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				58209F182E4B1CBF0012C395 /* Exceptions for "aDictionary" folder in "aDictionary" target */,
			);
			path = aDictionary;
			sourceTree = "<group>";
		};
		58209F092E4B1CBF0012C395 /* aDictionaryTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = aDictionaryTests;
			sourceTree = "<group>";
		};
		58209F132E4B1CBF0012C395 /* aDictionaryUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = aDictionaryUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		58209EF22E4B1CBD0012C395 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		58209F032E4B1CBF0012C395 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		58209F0D2E4B1CBF0012C395 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		58209EEC2E4B1CBD0012C395 = {
			isa = PBXGroup;
			children = (
				58209EF72E4B1CBD0012C395 /* aDictionary */,
				58209F092E4B1CBF0012C395 /* aDictionaryTests */,
				58209F132E4B1CBF0012C395 /* aDictionaryUITests */,
				58209EF62E4B1CBD0012C395 /* Products */,
			);
			sourceTree = "<group>";
		};
		58209EF62E4B1CBD0012C395 /* Products */ = {
			isa = PBXGroup;
			children = (
				58209EF52E4B1CBD0012C395 /* aDictionary.app */,
				58209F062E4B1CBF0012C395 /* aDictionaryTests.xctest */,
				58209F102E4B1CBF0012C395 /* aDictionaryUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		58209EF42E4B1CBD0012C395 /* aDictionary */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 58209F192E4B1CBF0012C395 /* Build configuration list for PBXNativeTarget "aDictionary" */;
			buildPhases = (
				58209EF12E4B1CBD0012C395 /* Sources */,
				58209EF22E4B1CBD0012C395 /* Frameworks */,
				58209EF32E4B1CBD0012C395 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				58209EF72E4B1CBD0012C395 /* aDictionary */,
			);
			name = aDictionary;
			packageProductDependencies = (
			);
			productName = aDictionary;
			productReference = 58209EF52E4B1CBD0012C395 /* aDictionary.app */;
			productType = "com.apple.product-type.application";
		};
		58209F052E4B1CBF0012C395 /* aDictionaryTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 58209F1E2E4B1CBF0012C395 /* Build configuration list for PBXNativeTarget "aDictionaryTests" */;
			buildPhases = (
				58209F022E4B1CBF0012C395 /* Sources */,
				58209F032E4B1CBF0012C395 /* Frameworks */,
				58209F042E4B1CBF0012C395 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				58209F082E4B1CBF0012C395 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				58209F092E4B1CBF0012C395 /* aDictionaryTests */,
			);
			name = aDictionaryTests;
			packageProductDependencies = (
			);
			productName = aDictionaryTests;
			productReference = 58209F062E4B1CBF0012C395 /* aDictionaryTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		58209F0F2E4B1CBF0012C395 /* aDictionaryUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 58209F212E4B1CBF0012C395 /* Build configuration list for PBXNativeTarget "aDictionaryUITests" */;
			buildPhases = (
				58209F0C2E4B1CBF0012C395 /* Sources */,
				58209F0D2E4B1CBF0012C395 /* Frameworks */,
				58209F0E2E4B1CBF0012C395 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				58209F122E4B1CBF0012C395 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				58209F132E4B1CBF0012C395 /* aDictionaryUITests */,
			);
			name = aDictionaryUITests;
			packageProductDependencies = (
			);
			productName = aDictionaryUITests;
			productReference = 58209F102E4B1CBF0012C395 /* aDictionaryUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		58209EED2E4B1CBD0012C395 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					58209EF42E4B1CBD0012C395 = {
						CreatedOnToolsVersion = 16.4;
					};
					58209F052E4B1CBF0012C395 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 58209EF42E4B1CBD0012C395;
					};
					58209F0F2E4B1CBF0012C395 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 58209EF42E4B1CBD0012C395;
					};
				};
			};
			buildConfigurationList = 58209EF02E4B1CBD0012C395 /* Build configuration list for PBXProject "aDictionary" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 58209EEC2E4B1CBD0012C395;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 58209EF62E4B1CBD0012C395 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				58209EF42E4B1CBD0012C395 /* aDictionary */,
				58209F052E4B1CBF0012C395 /* aDictionaryTests */,
				58209F0F2E4B1CBF0012C395 /* aDictionaryUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		58209EF32E4B1CBD0012C395 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		58209F042E4B1CBF0012C395 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		58209F0E2E4B1CBF0012C395 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		58209EF12E4B1CBD0012C395 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		58209F022E4B1CBF0012C395 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		58209F0C2E4B1CBF0012C395 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		58209F082E4B1CBF0012C395 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 58209EF42E4B1CBD0012C395 /* aDictionary */;
			targetProxy = 58209F072E4B1CBF0012C395 /* PBXContainerItemProxy */;
		};
		58209F122E4B1CBF0012C395 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 58209EF42E4B1CBD0012C395 /* aDictionary */;
			targetProxy = 58209F112E4B1CBF0012C395 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		58209F1A2E4B1CBF0012C395 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = aDictionary/aDictionary.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2XBCJAM843;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = aDictionary/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.aDictionary;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		58209F1B2E4B1CBF0012C395 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = aDictionary/aDictionary.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2XBCJAM843;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = aDictionary/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.aDictionary;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		58209F1C2E4B1CBF0012C395 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 2XBCJAM843;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		58209F1D2E4B1CBF0012C395 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 2XBCJAM843;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		58209F1F2E4B1CBF0012C395 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2XBCJAM843;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.aDictionaryTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/aDictionary.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/aDictionary";
			};
			name = Debug;
		};
		58209F202E4B1CBF0012C395 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2XBCJAM843;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.aDictionaryTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/aDictionary.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/aDictionary";
			};
			name = Release;
		};
		58209F222E4B1CBF0012C395 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2XBCJAM843;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.aDictionaryUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = aDictionary;
			};
			name = Debug;
		};
		58209F232E4B1CBF0012C395 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2XBCJAM843;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.sanva.aDictionaryUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = aDictionary;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		58209EF02E4B1CBD0012C395 /* Build configuration list for PBXProject "aDictionary" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58209F1C2E4B1CBF0012C395 /* Debug */,
				58209F1D2E4B1CBF0012C395 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		58209F192E4B1CBF0012C395 /* Build configuration list for PBXNativeTarget "aDictionary" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58209F1A2E4B1CBF0012C395 /* Debug */,
				58209F1B2E4B1CBF0012C395 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		58209F1E2E4B1CBF0012C395 /* Build configuration list for PBXNativeTarget "aDictionaryTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58209F1F2E4B1CBF0012C395 /* Debug */,
				58209F202E4B1CBF0012C395 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		58209F212E4B1CBF0012C395 /* Build configuration list for PBXNativeTarget "aDictionaryUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58209F222E4B1CBF0012C395 /* Debug */,
				58209F232E4B1CBF0012C395 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 58209EED2E4B1CBD0012C395 /* Project object */;
}
